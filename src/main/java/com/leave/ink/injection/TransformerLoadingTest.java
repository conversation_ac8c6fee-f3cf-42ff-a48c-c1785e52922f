package com.leave.ink.injection;

import com.external.ui.ExternalUI;

/**
 * TransformerLoader测试和监控工具
 * 用于验证智能加载系统的效果
 */
public class TransformerLoadingTest {
    
    /**
     * 测试智能加载系统
     */
    public static void testSmartLoading(TransformerLoader loader) {
        ExternalUI.log("[Test] Starting TransformerLoader smart loading test...");
        
        long startTime = System.currentTimeMillis();
        
        // 启用智能加载
        TransformerLoader.enableSmartLoading();
        
        // 执行加载
        loader.redefineClass();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        ExternalUI.log("[Test] Smart loading completed in " + totalTime + "ms");
        
        // 输出统计信息
        String stats = loader.getLoadingStats();
        ExternalUI.log("[Test] Loading Statistics:\n" + stats);
    }
    
    /**
     * 监控加载过程
     */
    public static void monitorLoading() {
        // 设置系统属性以启用详细日志
        System.setProperty("transformer.debug", "true");
        System.setProperty("transformer.detailed.logging", "true");
        
        ExternalUI.log("[Monitor] Transformer loading monitoring enabled");
    }
    
    /**
     * 验证所有transformer是否成功加载
     */
    public static boolean verifyAllTransformersLoaded(TransformerLoader loader) {
        // 这里可以添加具体的验证逻辑
        // 例如检查关键类是否被正确修改
        return true;
    }
}
