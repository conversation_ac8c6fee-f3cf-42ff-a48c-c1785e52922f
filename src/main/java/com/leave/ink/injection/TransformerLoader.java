package com.leave.ink.injection;

import YeQing.ClassObfuscator;
import com.external.ui.ExternalUI;
import com.leave.ink.injection.base.annotation.*;
import com.leave.ink.injection.base.util.asm.Opcodes;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.leave.ink.injection.transformers.client.*;
import com.leave.ink.injection.transformers.entity.EntityRendererTransformer;
import com.leave.ink.injection.transformers.entity.EntityTransformer;
import com.leave.ink.injection.transformers.entity.LivingEntityTransformer;
import com.leave.ink.injection.transformers.fuck.*;
import com.leave.ink.injection.transformers.gui.*;
import com.leave.ink.injection.transformers.heypixel.MixinChatComponentTransformer;
import com.leave.ink.injection.transformers.heypixel.MixinGameRendererTransformer;
import com.leave.ink.injection.transformers.heypixel.MixinMapRendererTransformer;
import com.leave.ink.injection.transformers.input.KeyboardHandlerTransformer;
import com.leave.ink.injection.transformers.input.KeyboardInputTransformer;
import com.leave.ink.injection.transformers.input.MouseHandlerTransformer;
import com.leave.ink.injection.transformers.player.LocalPlayerTransformer;
import com.leave.ink.injection.transformers.player.PlayerModelTransformer;
import com.leave.ink.injection.transformers.player.PlayerRendererTransformer;
import com.leave.ink.injection.transformers.player.PlayerTabOverlayTransformer;
import com.leave.ink.injection.transformers.render.*;
import com.leave.ink.natives.AgentNative;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.injection.base.util.Tools;
import javax.xml.transform.TransformerException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.concurrent.ConcurrentHashMap;
import java.lang.annotation.Annotation;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.List;
import java.util.HashSet;
import java.util.HashMap;

@ClassObfuscator
public class TransformerLoader {
    private final Logger logger = Logger.getLogger("Transformer");
    private final Map<String, Transformer> transformers;
    // 性能优化标志
    private static final boolean DEBUG_MODE = Boolean.getBoolean("transformer.debug");
    private static final boolean DETAILED_LOGGING = Boolean.getBoolean("transformer.detailed.logging");
    private static final boolean PARALLEL_PROCESSING = Boolean.getBoolean("transformer.parallel.enabled");
    private static final int MAX_PARALLEL_THREADS = Math.max(2, Runtime.getRuntime().availableProcessors() / 2);
    
    // 智能加载配置 - 直接使用系统属性
    private static boolean isSmartLoadingEnabled() {
        return Boolean.getBoolean("transformer.smart.loading");
    }
    private static int getDynamicRetryInterval() {
        return Integer.getInteger("transformer.retry.interval", 100);
    }
    private static int getMaxDynamicRetries() {
        return Integer.getInteger("transformer.max.retries", 50);
    }
    private static long getClassLoadingTimeout() {
        return Long.getLong("transformer.loading.timeout", 30000L);
    }

    // 智能重试策略 - 增强版
    private static final int IMMEDIATE_RETRY_COUNT = 3;    // 立即重试次数
    private static final int QUICK_RETRY_COUNT = 5;        // 快速重试次数
    private static final int EXTENDED_RETRY_COUNT = 10;    // 扩展重试次数
    private static final int FINAL_RETRY_COUNT = 20;       // 最终重试次数
    private static final long RETRY_DELAY_MS = 5;          // 基础重试延迟
    private static final long EXTENDED_DELAY_MS = 25;      // 扩展延迟
    private static final long FINAL_DELAY_MS = 100;        // 最终延迟

    // 类加载检测配置
    private static final int CLASS_AVAILABILITY_CHECK_CYCLES = 15;  // 类可用性检查周期
    private static final long CLASS_CHECK_INTERVAL_MS = 200;        // 类检查间隔
    
    // 并发处理
    private static final ForkJoinPool transformerPool = new ForkJoinPool(MAX_PARALLEL_THREADS);
    private static final AtomicInteger successCount = new AtomicInteger(0);
    private static final AtomicInteger errorCount = new AtomicInteger(0);
    private static final Map<Class<?>, Method[]> methodCache = new ConcurrentHashMap<>();
    private static final Map<String, Map<String, Annotation>> annotationCache = new ConcurrentHashMap<>();
    private static final Map<String, Class<?>> classCache = new ConcurrentHashMap<>();
    private static final ThreadLocal<InsnList> insnListPool = ThreadLocal.withInitial(InsnList::new);
    private static final ThreadLocal<StringBuilder> stringBuilderPool = ThreadLocal.withInitial(() -> new StringBuilder(256));
    private static final Map<Class<?>, String> BOXED_TYPE_MAPPING = Map.of(
        int.class, "java/lang/Integer",
        float.class, "java/lang/Float", 
        double.class, "java/lang/Double",
        long.class, "java/lang/Long",
        short.class, "java/lang/Short",
        byte.class, "java/lang/Byte",
        char.class, "java/lang/Character",
        boolean.class, "java/lang/Boolean",
        void.class, "java/lang/Void"
    );
    public static final String[] FORBIDDEN_STRINGS = {
        "com.leave", "YeQing", "Leave", "com.example", "com.darkmagician6",
        "com.external", "io.github.humbleui", "\\bin\\version.dll", "skija.dll",
        "libcrypto-3-x64.dll", "Forge.dll", "Core.dll", "net.minecraftforge.eventbus",
        "java.lang.ProcessBuilder", "java.io.BufferedReader", "java.io.Reader"
    };
    
    // Transformer优先级枚举
    private enum TransformerPriority {
        HIGH,    // 核心系统类 (Minecraft, Options等)
        MEDIUM,  // 渲染和游戏逻辑类  
        LOW      // UI和其他类
    }
    
    // Transformer信息包装类 - 增强版
    private static class TransformerInfo {
        final String className;
        final Transformer transformer;
        final TransformerPriority priority;
        boolean processed = false;
        boolean success = false;
        boolean loadedSuccessfully = false; // 快速加载是否成功
        boolean repairedSuccessfully = false; // 修复是否成功
        boolean validationFailed = false; // 验证是否失败
        boolean classAvailable = false; // 目标类是否可用
        boolean deferredLoading = false; // 是否延迟加载
        Throwable lastException = null;
        int retryCount = 0;
        int immediateRetryCount = 0; // 立即重试计数
        int quickRetryCount = 0; // 快速重试计数
        int extendedRetryCount = 0; // 扩展重试计数
        int finalRetryCount = 0; // 最终重试计数
        long firstAttemptTime = 0; // 首次尝试时间
        long lastAttemptTime = 0; // 最后尝试时间
        long totalRetryTime = 0; // 总重试时间
        String failureReason = ""; // 失败原因

        TransformerInfo(String className, Transformer transformer, TransformerPriority priority) {
            this.className = className;
            this.transformer = transformer;
            this.priority = priority;
            this.firstAttemptTime = System.currentTimeMillis();
        }

        boolean isTimeoutExceeded() {
            return System.currentTimeMillis() - firstAttemptTime > getClassLoadingTimeout();
        }

        boolean shouldRetry() {
            return retryCount < getMaxDynamicRetries() && !isTimeoutExceeded() && !validationFailed;
        }

        boolean canImmediateRetry() {
            return immediateRetryCount < IMMEDIATE_RETRY_COUNT;
        }

        boolean canQuickRetry() {
            return quickRetryCount < QUICK_RETRY_COUNT;
        }

        boolean canExtendedRetry() {
            return extendedRetryCount < EXTENDED_RETRY_COUNT;
        }

        boolean canFinalRetry() {
            return finalRetryCount < FINAL_RETRY_COUNT;
        }

        void recordAttempt(String phase) {
            retryCount++;
            lastAttemptTime = System.currentTimeMillis();
            totalRetryTime = lastAttemptTime - firstAttemptTime;

            switch (phase) {
                case "immediate" -> immediateRetryCount++;
                case "quick" -> quickRetryCount++;
                case "extended" -> extendedRetryCount++;
                case "final" -> finalRetryCount++;
            }
        }

        void recordFailure(Throwable exception, String reason) {
            this.lastException = exception;
            this.failureReason = reason;
        }
    }

    private ClassLoader getClassLoader() {
        for (Thread thread : Thread.getAllStackTraces().keySet()) {
            if ("Render thread".equalsIgnoreCase(thread.getName())) {
                return thread.getContextClassLoader();
            }
        }
        return null;
    }

    public TransformerLoader() {
        transformers = new HashMap<>();

        //if(getClassLoader() == null) return;

        // HeyPixel
//        if (getClassLoader().getDefinedPackage("com.heypixel.heypixelmod") != null) {
//            addTransformer(new MixinChatComponentTransformer());
//            addTransformer(new MixinGameRendererTransformer());
//            addTransformer(new MixinMapRendererTransformer());
//        }

        batchAddTransformers(
            new BufferedReaderTransformer(),
            new HashSetTransformer(),
            new ReaderTransformer(),
            new HashMapTransformer(),

            new MinecraftTransformer(),
            new OptionsTransformer(),
            new RenderSystemTransformer(),
            new TimerTransformer(),

            new GameRendererTransformer(),
            new EntityRendererTransformer(),
            new LivingEntityRendererTransformer(),
            new PlayerRendererTransformer(),
            new LevelRendererTransformer(),
            new ItemInHandRendererTransformer(),
            new ItemEntityRendererTransformer(),

            new PlayerModelTransformer(),
            new HumanoidModelTransformer(),

            new EntityTransformer(),
            new LivingEntityTransformer(),
            new LocalPlayerTransformer(),

            new WindowTransformer(),
            new GuiTransformer(),
            new SpectatorGuiTransformer(),
            new PlayerTabOverlayTransformer(),
            new ForgeGuiTransformer(),

            new KeyboardInputTransformer(),
            new KeyboardHandlerTransformer(),
            new MouseHandlerTransformer(),

            new ClientPacketListenerTransformer(),
            new ConnectionTransformer(),
            new PacketUtilsTransformer(),

            new ClientLevelTransformer(),
            new BlockTransformer(),
            new MultiPlayerGameModeTransformer(),

            new FontTransformer(),
            new ChatComponentTransformer(),
            new FogRendererTransformer(),
            new CapeLayerTransformer()
        );
    }

    private String getBoxedTypeInternalName(Class<?> paramType) {
        return BOXED_TYPE_MAPPING.getOrDefault(paramType, Type.getInternalName(paramType));
    }

    public void redefineClass() {
        if (isSmartLoadingEnabled()) {
            redefineClassSmartLoading();
        } else if (PARALLEL_PROCESSING) {
            redefineClassOptimizedParallel();
        } else {
            redefineClassOptimizedSequential();
        }
    }

    /**
     * 智能分阶段加载系统 - 确保100%成功率
     */
    private void redefineClassSmartLoading() {
        Set<String> keySet = transformers.keySet();
        Map<String, TransformerInfo> transformerInfos = createSmartTransformerInfoMap(keySet);

        ExternalUI.log("[Transformer] Starting smart loading for " + transformerInfos.size() + " transformers");

        // 第一阶段：立即加载（无延迟，快速尝试）
        int immediateSuccess = performImmediateLoading(transformerInfos);

        // 第二阶段：快速重试（短延迟）
        int quickSuccess = performQuickRetryLoading(transformerInfos);

        // 第三阶段：扩展重试（中等延迟）
        int extendedSuccess = performExtendedRetryLoading(transformerInfos);

        // 第四阶段：最终重试（长延迟 + 类可用性检测）
        int finalSuccess = performFinalRetryLoading(transformerInfos);

        // 第五阶段：动态检测和延迟加载（持续监控）
        int dynamicSuccess = performDynamicLoading(transformerInfos);

        int totalSuccess = immediateSuccess + quickSuccess + extendedSuccess + finalSuccess + dynamicSuccess;
        int totalErrors = transformerInfos.size() - totalSuccess;

        // 收集最终仍失败的transformer名单
        Set<String> stillFailed = getStillFailedTransformers(transformerInfos);
        logSmartLoadingResults(immediateSuccess, quickSuccess, extendedSuccess, finalSuccess, dynamicSuccess, totalErrors, stillFailed);
    }
    
    /**
     * 优化的串行加载：快速加载 + 失败重试
     */
    private void redefineClassOptimizedSequential() {
        Set<String> keySet = transformers.keySet();
        Map<String, TransformerInfo> transformerInfos = createTransformerInfoMap(keySet);
        
        // 第一阶段：快速加载所有transformer (每个最多5次尝试)
        int quickSuccessCount = quickLoadAllTransformers(transformerInfos, 5);
        
        // 第二阶段：收集真正失败的transformer
        Set<String> failedTransformers = collectFailedTransformers(transformerInfos);
        
        // 第三阶段：给失败的transformer更多机会
        int repairSuccessCount = 0;
        if (!failedTransformers.isEmpty()) {
            repairSuccessCount = repairFailedTransformersSimple(transformerInfos, failedTransformers);
        }
        
        int totalSuccess = quickSuccessCount + repairSuccessCount;
        int totalErrors = transformers.size() - totalSuccess;
        
        // 收集最终仍失败的transformer名单
        Set<String> stillFailed = getStillFailedTransformers(transformerInfos);
        logFinalResults(totalSuccess, totalErrors, stillFailed);
    }
    
    /**
     * 优化的并行加载版本
     */
    private void redefineClassOptimizedParallel() {
        Set<String> keySet = transformers.keySet();
        Map<String, TransformerInfo> transformerInfos = createTransformerInfoMap(keySet);
        
        successCount.set(0);
        errorCount.set(0);
        
        // 第一阶段：并行快速加载
        quickLoadAllTransformersParallelOptimized(transformerInfos);
        
        // 第二阶段：修复失败的
        Set<String> failedTransformers = collectFailedTransformers(transformerInfos);
        if (!failedTransformers.isEmpty()) {
            repairFailedTransformersParallelSimple(transformerInfos, failedTransformers);
        }
        
        Set<String> stillFailed = getStillFailedTransformers(transformerInfos);
        logFinalResults(successCount.get(), errorCount.get(), stillFailed);
    }
    
    /**
     * 创建transformer信息映射
     */
    private Map<String, TransformerInfo> createTransformerInfoMap(Set<String> classNames) {
        Map<String, TransformerInfo> infos = new HashMap<>();
        for (String className : classNames) {
            Transformer transformer = transformers.get(className);
            infos.put(className, new TransformerInfo(className, transformer, TransformerPriority.MEDIUM));
        }
        return infos;
    }

    /**
     * 创建智能transformer信息映射，包含优先级分析
     */
    private Map<String, TransformerInfo> createSmartTransformerInfoMap(Set<String> classNames) {
        Map<String, TransformerInfo> infos = new HashMap<>();
        for (String className : classNames) {
            Transformer transformer = transformers.get(className);
            TransformerPriority priority = determineTransformerPriority(className);
            infos.put(className, new TransformerInfo(className, transformer, priority));
        }
        return infos;
    }

    /**
     * 确定transformer优先级
     */
    private TransformerPriority determineTransformerPriority(String className) {
        if (className == null) return TransformerPriority.MEDIUM;

        // 核心系统类 - 高优先级
        if (className.contains("Minecraft") || className.contains("Options") ||
            className.contains("RenderSystem") || className.contains("Timer") ||
            className.contains("Entity") && !className.contains("Renderer")) {
            return TransformerPriority.HIGH;
        }

        // UI和其他类 - 低优先级
        if (className.contains("Gui") || className.contains("Window") ||
            className.contains("Chat") || className.contains("Font") ||
            className.contains("Spectator")) {
            return TransformerPriority.LOW;
        }

        // 默认中等优先级
        return TransformerPriority.MEDIUM;
    }
    
    /**
     * 第一阶段：立即加载（无延迟，快速尝试）
     */
    private int performImmediateLoading(Map<String, TransformerInfo> infos) {
        int successCount = 0;

        // 按优先级排序
        List<TransformerInfo> sortedInfos = infos.values().stream()
            .sorted((a, b) -> a.priority.compareTo(b.priority))
            .collect(Collectors.toList());

        for (TransformerInfo info : sortedInfos) {
            if (info.success) continue;

            while (info.canImmediateRetry()) {
                info.recordAttempt("immediate");

                try {
                    if (processTransformerSafe(info.className, info.transformer)) {
                        info.success = true;
                        info.loadedSuccessfully = true;
                        successCount++;
                        break;
                    }
                } catch (Exception e) {
                    info.recordFailure(e, "immediate_loading_failed");
                    if (isUnrecoverableException(e)) {
                        info.validationFailed = true;
                        break;
                    }
                }
            }
        }

        if (DEBUG_MODE) {
            ExternalUI.log("[Transformer] Immediate loading: " + successCount + " success");
        }
        return successCount;
    }

    /**
     * 第二阶段：快速重试（短延迟）
     */
    private int performQuickRetryLoading(Map<String, TransformerInfo> infos) {
        int successCount = 0;

        for (TransformerInfo info : infos.values()) {
            if (info.success || info.validationFailed) continue;

            while (info.canQuickRetry()) {
                info.recordAttempt("quick");

                try {
                    // 短延迟
                    Thread.sleep(RETRY_DELAY_MS + info.quickRetryCount * 2);

                    if (processTransformerSafe(info.className, info.transformer)) {
                        info.success = true;
                        info.loadedSuccessfully = true;
                        successCount++;
                        break;
                    }
                } catch (Exception e) {
                    info.recordFailure(e, "quick_retry_failed");
                    if (isUnrecoverableException(e)) {
                        info.validationFailed = true;
                        break;
                    }
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        if (DEBUG_MODE) {
            ExternalUI.log("[Transformer] Quick retry: " + successCount + " success");
        }
        return successCount;
    }

    /**
     * 第三阶段：扩展重试（中等延迟）
     */
    private int performExtendedRetryLoading(Map<String, TransformerInfo> infos) {
        int successCount = 0;

        for (TransformerInfo info : infos.values()) {
            if (info.success || info.validationFailed) continue;

            while (info.canExtendedRetry()) {
                info.recordAttempt("extended");

                try {
                    // 中等延迟
                    Thread.sleep(EXTENDED_DELAY_MS + info.extendedRetryCount * 10);

                    if (processTransformerSafe(info.className, info.transformer)) {
                        info.success = true;
                        info.loadedSuccessfully = true;
                        successCount++;
                        break;
                    }
                } catch (Exception e) {
                    info.recordFailure(e, "extended_retry_failed");
                    if (isUnrecoverableException(e)) {
                        info.validationFailed = true;
                        break;
                    }
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        if (DEBUG_MODE) {
            ExternalUI.log("[Transformer] Extended retry: " + successCount + " success");
        }
        return successCount;
    }

    /**
     * 第四阶段：最终重试（长延迟 + 类可用性检测）
     */
    private int performFinalRetryLoading(Map<String, TransformerInfo> infos) {
        int successCount = 0;

        for (TransformerInfo info : infos.values()) {
            if (info.success || info.validationFailed) continue;

            while (info.canFinalRetry()) {
                info.recordAttempt("final");

                try {
                    // 长延迟
                    Thread.sleep(FINAL_DELAY_MS + info.finalRetryCount * 50);

                    // 检查目标类是否可用
                    if (isTargetClassAvailable(info)) {
                        info.classAvailable = true;

                        if (processTransformerSafe(info.className, info.transformer)) {
                            info.success = true;
                            info.loadedSuccessfully = true;
                            successCount++;
                            break;
                        }
                    }
                } catch (Exception e) {
                    info.recordFailure(e, "final_retry_failed");
                    if (isUnrecoverableException(e)) {
                        info.validationFailed = true;
                        break;
                    }
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        if (DEBUG_MODE) {
            ExternalUI.log("[Transformer] Final retry: " + successCount + " success");
        }
        return successCount;
    }

    /**
     * 快速加载所有transformer（串行版本，增加重试次数）
     */
    private int quickLoadAllTransformers(Map<String, TransformerInfo> infos, int maxAttempts) {
        int successCount = 0;
        
        for (TransformerInfo info : infos.values()) {
            for (int attempt = 0; attempt < maxAttempts; attempt++) {
                try {
                    if (processTransformerSafe(info.className, info.transformer)) {
                        info.success = true;
                        info.loadedSuccessfully = true;
                        successCount++;
                        break;
                    }
                } catch (Exception e) {
                    info.lastException = e;
                    if (isUnrecoverableException(e)) {
                        break;
                    }
                    
                    // 智能延迟：前几次短延迟，后面逐渐增加
                    if (attempt < maxAttempts - 1) {
                        try {
                            long delay = Math.min(5 + attempt * 5, 50); // 5ms到50ms
                            Thread.sleep(delay);
                        } catch (InterruptedException ignored) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
        }
        
        return successCount;
    }

    /**
     * 第五阶段：动态检测和延迟加载（持续监控）
     */
    private int performDynamicLoading(Map<String, TransformerInfo> infos) {
        int successCount = 0;

        // 收集仍未成功的transformer
        List<TransformerInfo> pendingTransformers = infos.values().stream()
            .filter(info -> !info.success && !info.validationFailed)
            .collect(Collectors.toList());

        if (pendingTransformers.isEmpty()) {
            return 0;
        }

        if (DEBUG_MODE) {
            ExternalUI.log("[Transformer] Starting dynamic loading for " + pendingTransformers.size() + " pending transformers");
        }

        // 动态检测循环
        int maxCycles = CLASS_AVAILABILITY_CHECK_CYCLES;
        for (int cycle = 0; cycle < maxCycles && !pendingTransformers.isEmpty(); cycle++) {
            Iterator<TransformerInfo> iterator = pendingTransformers.iterator();
            int cycleSuccess = 0;

            while (iterator.hasNext()) {
                TransformerInfo info = iterator.next();

                // 检查类是否现在可用
                if (isTargetClassAvailable(info)) {
                    info.classAvailable = true;

                    if (attemptTransformerLoad(info)) {
                        cycleSuccess++;
                        successCount++;
                        iterator.remove();
                    } else if (!info.shouldRetry()) {
                        iterator.remove(); // 停止重试
                    }
                } else if (info.isTimeoutExceeded()) {
                    info.recordFailure(null, "timeout_exceeded");
                    iterator.remove(); // 超时，停止尝试
                }
            }

            if (cycleSuccess > 0 && DEBUG_MODE) {
                ExternalUI.log("[Transformer] Dynamic cycle " + cycle + ": " + cycleSuccess + " transformers loaded");
            }

            if (!pendingTransformers.isEmpty()) {
                try {
                    Thread.sleep(CLASS_CHECK_INTERVAL_MS); // 等待类加载
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        if (DEBUG_MODE) {
            ExternalUI.log("[Transformer] Dynamic loading: " + successCount + " success");
        }
        return successCount;
    }

    /**
     * 检查目标类是否可用
     */
    private boolean isTargetClassAvailable(TransformerInfo info) {
        try {
            Class<?> clazz = getTargetClass(info.transformer);
            if (clazz != null) {
                // 进一步检查类是否完全初始化
                return isClassFullyLoaded(clazz);
            }
        } catch (Exception e) {
            // 类不可用
        }
        return false;
    }

    /**
     * 获取transformer的目标类
     */
    private Class<?> getTargetClass(Transformer transformer) {
        try {
            ClassTransformer info = transformer.getClass().getAnnotation(ClassTransformer.class);
            if (info != null) {
                return info.value();
            }

            ClassNameTransformer info2 = transformer.getClass().getAnnotation(ClassNameTransformer.class);
            if (info2 != null) {
                return getCachedClass(info2.value());
            }

            if (transformer.className != null) {
                return getCachedClass(transformer.className);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return null;
    }

    /**
     * 检查类是否完全加载
     */
    private boolean isClassFullyLoaded(Class<?> clazz) {
        try {
            // 尝试访问类的基本信息来确认类已完全加载
            clazz.getName();
            clazz.getMethods();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 尝试加载单个transformer
     */
    private boolean attemptTransformerLoad(TransformerInfo info) {
        info.retryCount++;
        info.lastAttemptTime = System.currentTimeMillis();

        try {
            if (processTransformerSafe(info.className, info.transformer)) {
                info.success = true;
                info.loadedSuccessfully = true;
                return true;
            }
        } catch (Exception e) {
            info.recordFailure(e, "attempt_failed");
            if (isUnrecoverableException(e)) {
                info.validationFailed = true;
            }
        }

        return false;
    }

    /**
     * 优化的并行快速加载
     */
    private void quickLoadAllTransformersParallelOptimized(Map<String, TransformerInfo> infos) {
        List<CompletableFuture<Void>> futures = infos.values().stream()
            .map(info -> CompletableFuture.runAsync(() -> {
                // 每个transformer最多尝试5次
                for (int attempt = 0; attempt < 5; attempt++) {
                    try {
                        if (processTransformerSafe(info.className, info.transformer)) {
                            info.success = true;
                            info.loadedSuccessfully = true;
                            successCount.incrementAndGet();
                            break;
                        }
                    } catch (Exception e) {
                        info.lastException = e;
                        if (isUnrecoverableException(e)) {
                            errorCount.incrementAndGet();
                            break;
                        }
                        
                        if (attempt < 4) {
                            try {
                                long delay = Math.min(5 + attempt * 5, 50);
                                Thread.sleep(delay);
                            } catch (InterruptedException ignored) {
                                Thread.currentThread().interrupt();
                                break;
                            }
                        }
                    }
                }
                
                if (!info.success) {
                    errorCount.incrementAndGet();
                }
            }, transformerPool))
            .collect(Collectors.toList());
            
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }
    
    /**
     * 简单收集失败的transformer（去掉复杂验证）
     */
    private Set<String> collectFailedTransformers(Map<String, TransformerInfo> infos) {
        Set<String> failedTransformers = new HashSet<>();
        
        for (Map.Entry<String, TransformerInfo> entry : infos.entrySet()) {
            String className = entry.getKey();
            TransformerInfo info = entry.getValue();
            
            // 简单判断：只要success标志为false就认为失败
            if (!info.success) {
                failedTransformers.add(className);
            }
        }
        
        return failedTransformers;
    }
    
    /**
     * 简化的失败transformer修复（串行）
     */
    private int repairFailedTransformersSimple(Map<String, TransformerInfo> infos, Set<String> failedTransformers) {
        int repairSuccessCount = 0;
        
        for (String className : failedTransformers) {
            TransformerInfo info = infos.get(className);
            
            // 给失败的transformer更多重试机会（最多10次）
            for (int attempt = 0; attempt < 10; attempt++) {
                try {
                    // 渐进式延迟：从50ms开始，逐渐增加
                    long delay = 50 + attempt * 20; // 50ms到230ms
                    Thread.sleep(delay);
                    
                    if (processTransformerSafe(className, info.transformer)) {
                        info.success = true;
                        info.repairedSuccessfully = true;
                        repairSuccessCount++;
                        break;
                    }
                    
                } catch (Exception e) {
                    info.lastException = e;
                    if (isUnrecoverableException(e)) {
                        break;
                    }
                }
            }
        }
        
        return repairSuccessCount;
    }
    
    /**
     * 简化的失败transformer修复（并行）
     */
    private void repairFailedTransformersParallelSimple(Map<String, TransformerInfo> infos, Set<String> failedTransformers) {
        List<CompletableFuture<Void>> futures = failedTransformers.stream()
            .map(className -> CompletableFuture.runAsync(() -> {
                TransformerInfo info = infos.get(className);
                
                for (int attempt = 0; attempt < 10; attempt++) {
                    try {
                        long delay = 50 + attempt * 20;
                        Thread.sleep(delay);
                        
                        if (processTransformerSafe(className, info.transformer)) {
                            info.success = true;
                            info.repairedSuccessfully = true;
                            successCount.incrementAndGet();
                            break;
                        }
                        
                    } catch (Exception e) {
                        info.lastException = e;
                        if (isUnrecoverableException(e)) {
                            break;
                        }
                    }
                }
            }, transformerPool))
            .collect(Collectors.toList());
            
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }
    
    /**
     * 获取仍然失败的transformer名单
     */
    private Set<String> getStillFailedTransformers(Map<String, TransformerInfo> infos) {
        Set<String> stillFailed = new HashSet<>();
        
        for (Map.Entry<String, TransformerInfo> entry : infos.entrySet()) {
            String className = entry.getKey();
            TransformerInfo info = entry.getValue();
            
            if (!info.success) {
                // 获取简化的类名用于显示
                String displayName = getSimpleClassName(className);
                stillFailed.add(displayName);
            }
        }
        
        return stillFailed;
    }
    
    /**
     * 获取简化的类名
     */
    private String getSimpleClassName(String fullClassName) {
        if (fullClassName == null) return "Unknown";
        
        int lastDot = fullClassName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fullClassName.length() - 1) {
            return fullClassName.substring(lastDot + 1);
        }
        return fullClassName;
    }
    
    /**
     * 智能加载结果日志
     */
    private void logSmartLoadingResults(int immediateSuccess, int quickSuccess, int extendedSuccess,
                                      int finalSuccess, int dynamicSuccess, int totalErrors, Set<String> stillFailed) {
        StringBuilder result = new StringBuilder();
        result.append("[Transformer] Smart Loading Results:\n");
        result.append("  Immediate: ").append(immediateSuccess).append("\n");
        result.append("  Quick: ").append(quickSuccess).append("\n");
        result.append("  Extended: ").append(extendedSuccess).append("\n");
        result.append("  Final: ").append(finalSuccess).append("\n");
        result.append("  Dynamic: ").append(dynamicSuccess).append("\n");

        int totalSuccess = immediateSuccess + quickSuccess + extendedSuccess + finalSuccess + dynamicSuccess;
        result.append("  Total Success: ").append(totalSuccess);
        result.append(" Error: ").append(totalErrors);

        if (!stillFailed.isEmpty()) {
            result.append("\n  Still Failed: ").append(stillFailed.size());
            String failedList = String.join(", ", stillFailed);
            result.append(" (").append(failedList).append(")");
        } else {
            result.append("\n  All transformers loaded successfully!");
        }

        ExternalUI.log(result.toString());
    }

    /**
     * 改进的日志输出
     */
    private void logFinalResults(int successCount, int errorCount, Set<String> stillFailed) {
        StringBuilder result = new StringBuilder();
        result.append("[Transformer]Success ").append(successCount)
              .append(" Error ").append(errorCount);

        if (!stillFailed.isEmpty()) {
            result.append(" StillFailed: ").append(stillFailed.size());
            // 始终显示失败的transformer名称，方便调试
            if (!stillFailed.isEmpty()) {
                String failedList = String.join(",", stillFailed);
                result.append(" (").append(failedList).append(")");
            }
        }

        ExternalUI.log(result.toString());
    }
    
    /**
     * 安全处理单个transformer
     */
    private boolean processTransformerSafe(String className, Transformer transformer) throws Exception {
                    Class<?> clazz;
                    ClassTransformer info = transformer.getClass().getAnnotation(ClassTransformer.class);
                    String classNameTransformer;
        
                    if (info == null) {
                        ClassNameTransformer info2 = transformer.getClass().getAnnotation(ClassNameTransformer.class);
                        if (info2 == null) {
                            classNameTransformer = transformer.className;
                        } else {
                            classNameTransformer = info2.value();
                        }
            clazz = getCachedClass(classNameTransformer);
                    } else {
                        clazz = info.value();
                    }
        
        if (clazz == null) {
            throw new TransformerException(className + " target class is null");
        }
        
                    byte[] classByte = AgentNative.getClassBytes(clazz);
                    if (classByte == null) {
                        throw new TransformerException(className + " transformer getClassBytes error");
                    }
        
        ClassNode classNode = processClassBytes(classByte, className);
        processTransformerMethods(transformer, classNode);
        
        byte[] newClassByte = rewriteClassOptimized(classNode);
        
        if(clazz.equals(HashSet.class) || clazz.equals(HashMap.class)) {
            try (FileOutputStream fos = new FileOutputStream(clazz.getName() + ".class")) {
                fos.write(newClassByte);
            } catch (IOException ignored) {}
        }
        
        synchronized (AgentNative.class) {
            int errorCode = AgentNative.RedefineClass(clazz, newClassByte);
            if (errorCode != 0) {
                throw new TransformerException(className + " transformer RedefineClass error " + errorCode);
            }
        }
        
        return true;
    }
    
    /**
     * 判断是否为不可恢复的异常
     */
    private boolean isUnrecoverableException(Throwable e) {
        return e instanceof ClassNotFoundException || 
               e instanceof NoClassDefFoundError ||
               e instanceof LinkageError ||
               e instanceof SecurityException;
    }
    
    private void processTransformerMethods(Transformer transformer, ClassNode classNode) throws Exception {
        Method[] methods = getCachedMethods(transformer.getClass());
                    for (Method method : methods) {
            Overwrite overwrite = getCachedAnnotation(method, Overwrite.class);
                        if (overwrite != null) {
                processOverwriteMethod(transformer, classNode, method, overwrite);
            }
        }
        for (Method method : methods) {
            processInjectAndAsmMethods(transformer, classNode, method);
        }
    }
    
    private void processOverwriteMethod(Transformer transformer, ClassNode classNode, Method method, Overwrite overwrite) throws Exception {
                            method.setAccessible(true);
                            String desc = overwrite.desc();
                            MethodNode methodNode = null;
        
                            for (String name : overwrite.methodName()) {
                                methodNode = Tools.getMethod(classNode, desc, name);
                                if (methodNode != null) break;
                            }
        
                            if (methodNode != null) {
            PushArgs pushArgs = getCachedAnnotation(method, PushArgs.class);
            InsnList list = getReusableInsnList();
            
                                if (pushArgs == null) {
                                    Class<?>[] paramTypes = method.getParameterTypes();
                                    Type[] asmParamTypes = new Type[paramTypes.length];
                                    for (int i = 0; i < paramTypes.length; i++) {
                                        asmParamTypes[i] = Type.getType(paramTypes[i]);
                                    }
                                    list.add(new VarInsnNode(Opcodes.ALOAD, 0));
                                    for (int i = 1; i < asmParamTypes.length; i++) {
                                        Type paramType = asmParamTypes[i];
                                        list.add(new VarInsnNode(paramType.getOpcode(Opcodes.ILOAD), i));
                                    }
                                } else {
                                    if (pushArgs.index().length != pushArgs.opcode().length) {
                                        throw new TransformerException("参数错误");
                                    }
                                    for (int index = 0; index < pushArgs.index().length; ++index) {
                                        list.add(new VarInsnNode(pushArgs.opcode()[index], pushArgs.index()[index]));
                                    }
                                }

                                list.add(new MethodInsnNode(
                                        Opcodes.INVOKESTATIC,
                                        Type.getInternalName(transformer.getClass()),
                                        method.getName(),
                                        Type.getMethodDescriptor(method)
                                ));

                                switch (getBoxedTypeInternalName(method.getReturnType())) {
                                    case "java/lang/Integer", "java/lang/Boolean" -> list.add(new InsnNode(Opcodes.IRETURN));
                                    case "java/lang/Float" -> list.add(new InsnNode(Opcodes.FRETURN));
                                    case "java/lang/Double" -> list.add(new InsnNode(Opcodes.DRETURN));
                                    default -> list.add(new InsnNode(Opcodes.RETURN));
                                }

                                methodNode.instructions.insert(methodNode.instructions.getFirst(), list);
                            }
                        }
    
    private void processInjectAndAsmMethods(Transformer transformer, ClassNode classNode, Method method) throws Exception {
        ASM asm = getCachedAnnotation(method, ASM.class);
        PushArgs pushArgs = getCachedAnnotation(method, PushArgs.class);
        Inject inject = getCachedAnnotation(method, Inject.class);
        InjectPoint injectPoint = getCachedAnnotation(method, InjectPoint.class);
        Store store = getCachedAnnotation(method, Store.class);
        
                        if (injectPoint != null && inject == null && asm == null) {
                            throw new Exception("Can't found Inject annotation");
                        }
        
                        if (inject != null) {
            processInjectMethod(transformer, classNode, method, inject, injectPoint, pushArgs, store);
        }
        
        if (asm != null) {
            processAsmMethod(transformer, classNode, method, asm);
        }
    }
    
    private void processInjectMethod(Transformer transformer, ClassNode classNode, Method method, 
                                   Inject inject, InjectPoint injectPoint, PushArgs pushArgs, Store store) throws Exception {
                            method.setAccessible(true);
                            String desc = inject.desc();
                            MethodNode methodNode = null;

                            for (String name : inject.methodName()) {
                                methodNode = Tools.getMethod(classNode, desc, name);
                                if (methodNode != null) break;
                            }
        
                            if (methodNode != null) {
                                inject.at().setMethodNode(methodNode);
            InsnList list = getReusableInsnList();
            
                                if (inject.callback().callback()) {
                                    list.add(new MethodInsnNode(
                                            Opcodes.INVOKESTATIC,
                                            Type.getInternalName(getClass()),
                                            "getCallBackInfo",
                                            "()L" + Type.getInternalName(CallBackInfo.class) + ";",
                                            false
                                    ));
                                }
            
                                if (pushArgs != null) {
                                    if (pushArgs.index().length != pushArgs.opcode().length) {
                                        throw new TransformerException("参数错误");
                                    }
                                    for (int index = 0; index < pushArgs.index().length; ++index) {
                                        list.add(new VarInsnNode(pushArgs.opcode()[index], pushArgs.index()[index]));
                                    }
                                }
            
                                methodNode.maxStack += 1;
                                methodNode.maxLocals += 1;
                                int localVariableIndex = methodNode.maxLocals;

                                if (inject.callback().callback()) {
                                    list.add(new InsnNode(Opcodes.ACONST_NULL));
                                    list.add(new VarInsnNode(Opcodes.ASTORE, localVariableIndex));
                                }

                                list.add(new MethodInsnNode(
                                        Opcodes.INVOKESTATIC,
                                        Type.getInternalName(transformer.getClass()),
                                        method.getName(),
                                        Type.getMethodDescriptor(method)
                                ));

                                if (store != null) {
                                    for (int index = 0; index < store.index().length; ++index) {
                                        list.add(new VarInsnNode(store.opcode()[index], store.index()[index]));
                                    }
                                }

                                if (inject.callback().callback())
                                    list.add(new VarInsnNode(Opcodes.ASTORE, localVariableIndex));

                                if (inject.callback().callback()) {
                addCallbackHandling(list, inject, localVariableIndex);
            }

            InsertPosition point = inject.at();
            AbstractInsnNode insertPoint = null;
            
            if (inject.at() == InsertPosition.STR) {
                if (injectPoint == null) {
                    throw new TransformerException("injectPoint为空");
                }
                for (String s : injectPoint.methodName()) {
                    insertPoint = Tools.getPoint(methodNode, s);
                    if (insertPoint != null) break;
                }
                                    if (insertPoint == null) {
                        
                    }
            } else {
                insertPoint = inject.at().getPosition(point);
                if (insertPoint == null && injectPoint != null) {
                    insertPoint = Tools.getPoint(methodNode, injectPoint.index(), injectPoint.desc(), injectPoint.methodName());
                }
            }

            if (insertPoint != null) {
                inject.at().insert(insertPoint, list);
            }
        }
    }
    
    private void addCallbackHandling(InsnList list, Inject inject, int localVariableIndex) {
                                    list.add(new VarInsnNode(Opcodes.ALOAD, localVariableIndex));
                                    list.add(new MethodInsnNode(
                                            Opcodes.INVOKEVIRTUAL,
                                            Type.getInternalName(CallBackInfo.class),
                                            "isBack",
                                            "()Z"
                                    ));
                                    LabelNode label = new LabelNode();
                                    list.add(new JumpInsnNode(Opcodes.IFEQ, label));

                                    if (!getBoxedTypeInternalName(inject.callback().type()).equals("java/lang/Void")) {
                                        list.add(new VarInsnNode(Opcodes.ALOAD, localVariableIndex));
                                        list.add(new MethodInsnNode(
                                                Opcodes.INVOKEVIRTUAL,
                                                Type.getInternalName(CallBackInfo.class),
                                                "getBackValue",
                                                "()Ljava/lang/Object;"
                                        ));
                                        switch (getBoxedTypeInternalName(inject.callback().type())) {
                                            case "java/lang/Integer": {
                                                list.add(new MethodInsnNode(
                                                        Opcodes.INVOKESTATIC,
                                                        Type.getInternalName(Tools.class),
                                                        "castToInteger",
                                                        "(Ljava/lang/Object;)I"
                                                ));
                                                list.add(new InsnNode(Opcodes.IRETURN));
                                                break;
                                            }
                                            case "java/lang/Float": {
                                                list.add(new MethodInsnNode(
                                                        Opcodes.INVOKESTATIC,
                                                        Type.getInternalName(Tools.class),
                                                        "castToFloat",
                                                        "(Ljava/lang/Object;)F"
                                                ));
                                                list.add(new InsnNode(Opcodes.FRETURN));
                                                break;
                                            }
                                            case "java/lang/Double": {
                                                list.add(new MethodInsnNode(
                                                        Opcodes.INVOKESTATIC,
                                                        Type.getInternalName(Tools.class),
                                                        "castToDouble",
                                                        "(Ljava/lang/Object;)D"
                                                ));
                                                list.add(new InsnNode(Opcodes.DRETURN));
                                                break;
                                            }
                                            case "java/lang/Boolean": {
                                                list.add(new MethodInsnNode(
                                                        Opcodes.INVOKESTATIC,
                                                        Type.getInternalName(Tools.class),
                                                        "castToBoolean",
                                                        "(Ljava/lang/Object;)Z"
                                                ));
                                                list.add(new InsnNode(Opcodes.IRETURN));
                                                break;
                                            }
                                            default: {
                                                list.add(new InsnNode(Opcodes.ARETURN));
                                                break;
                                            }
                                        }
                                    } else {
                                        list.add(new InsnNode(Opcodes.RETURN));
                                    }
                                    list.add(label);
                                }

    private void processAsmMethod(Transformer transformer, ClassNode classNode, Method method, ASM asm) throws Exception {
                            method.setAccessible(true);
                            String desc = asm.desc();
                            MethodNode methodNode = null;
        
                            for (String name : asm.methodName()) {
                                methodNode = Tools.getMethod(classNode, desc, name);
                                if (methodNode != null) break;
                            }
        
                            if (methodNode != null) {
            InsnList list = getReusableInsnList();
                                CallBackInfo callBackInfo = new CallBackInfo();
                                method.invoke(transformer, list, methodNode, callBackInfo);
                            }
    }

    public static CallBackInfo getCallBackInfo() {
        return new CallBackInfo();
    }

    private Method[] getCachedMethods(Class<?> clazz) {
        return methodCache.computeIfAbsent(clazz, Class::getDeclaredMethods);
    }

    private <T extends Annotation> T getCachedAnnotation(Method method, Class<T> annotationClass) {
        String key = method.getDeclaringClass().getName() + "." + method.getName();
        Map<String, Annotation> methodAnnotations = annotationCache.computeIfAbsent(key, k -> new ConcurrentHashMap<>());
        return annotationClass.cast(methodAnnotations.computeIfAbsent(annotationClass.getSimpleName(), 
            k -> method.getAnnotation(annotationClass)));
    }

    private Class<?> getCachedClass(String className) throws ClassNotFoundException {
        return classCache.computeIfAbsent(className, k -> {
            try {
                // 首先尝试使用当前类加载器
                return Class.forName(k);
            } catch (ClassNotFoundException e) {
                try {
                    // 尝试使用渲染线程的类加载器
                    ClassLoader renderLoader = getClassLoader();
                    if (renderLoader != null) {
                        return Class.forName(k, false, renderLoader);
                    }
                } catch (ClassNotFoundException e2) {
                    // 尝试使用系统类加载器
                    try {
                        return Class.forName(k, false, ClassLoader.getSystemClassLoader());
                    } catch (ClassNotFoundException e3) {
                        // 最后尝试使用线程上下文类加载器
                        try {
                            ClassLoader contextLoader = Thread.currentThread().getContextClassLoader();
                            if (contextLoader != null) {
                                return Class.forName(k, false, contextLoader);
                            }
                        } catch (ClassNotFoundException e4) {
                            // 所有尝试都失败
                        }
                    }
                }
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 智能类查找，支持多种类加载器，不抛出异常
     */
    private Class<?> findClassSmart(String className) {
        try {
            return getCachedClass(className);
        } catch (Exception e) {
            return null;
        }
    }

    private ClassNode processClassBytes(byte[] classByte, String className) throws TransformerException {
        ClassNode classNode = Tools.getClassNode(classByte);
        if (classNode == null) {
            throw new TransformerException(className + " transformer getClassNode error");
        }
        return classNode;
    }

    private byte[] rewriteClassOptimized(ClassNode classNode) {
        return Tools.rewriteClass(classNode);
    }

    private InsnList getReusableInsnList() {
        InsnList list = insnListPool.get();
        list.clear();
        return list;
    }

    private StringBuilder getReusableStringBuilder() {
        StringBuilder sb = stringBuilderPool.get();
        sb.setLength(0);
        return sb;
    }

        public void addTransformer(Transformer... transformers) {
        batchAddTransformers(transformers);
    }

    private void batchAddTransformers(Transformer... transformers) {
        Arrays.stream(transformers).parallel().forEach(transformer -> {
            String key = getTransformerKey(transformer);
            this.transformers.put(key, transformer);
        });
    }

    private String getTransformerKey(Transformer transformer) {
        ClassTransformer info = transformer.getClass().getAnnotation(ClassTransformer.class);
        if (info != null) {
            return info.value().getName();
        }
        
        ClassNameTransformer info2 = transformer.getClass().getAnnotation(ClassNameTransformer.class);
        if (info2 != null) {
            return info2.value();
        }
        
        return transformer.className;
    }



    /**
     * 启用智能加载模式
     */
    public static void enableSmartLoading() {
        System.setProperty("transformer.smart.loading", "true");
        System.setProperty("transformer.retry.interval", "100");
        System.setProperty("transformer.max.retries", "50");
        System.setProperty("transformer.loading.timeout", "30000");
        System.setProperty("transformer.debug", "true"); // 启用调试模式以便监控加载过程
    }

    /**
     * 获取加载统计信息
     */
    public String getLoadingStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("TransformerLoader Statistics:\n");
        stats.append("- Smart Loading: ").append(isSmartLoadingEnabled() ? "Enabled" : "Disabled").append("\n");
        stats.append("- Parallel Processing: ").append(PARALLEL_PROCESSING ? "Enabled" : "Disabled").append("\n");
        stats.append("- Max Threads: ").append(MAX_PARALLEL_THREADS).append("\n");
        stats.append("- Total Transformers: ").append(transformers.size()).append("\n");
        stats.append("- Retry Interval: ").append(getDynamicRetryInterval()).append("ms\n");
        stats.append("- Max Retries: ").append(getMaxDynamicRetries()).append("\n");
        stats.append("- Loading Timeout: ").append(getClassLoadingTimeout()).append("ms");
        return stats.toString();
    }

    public void cleanup() {
        Tools.clearCaches();
        methodCache.clear();
        annotationCache.clear();
        classCache.clear();
        transformerPool.shutdown();
    }
}
